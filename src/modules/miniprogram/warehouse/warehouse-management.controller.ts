import { Controller, Post, Get, Body, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { WarehouseManagementService } from './warehouse-management.service'
import { OutboundService } from './services/outbound.service'
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard'
import { Public } from '../../auth/decorators/public.decorator'

@ApiTags('小程序-仓库管理')
@Controller('miniprogram/warehouse-management')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WarehouseManagementController {
  constructor(
    private readonly warehouseManagementService: WarehouseManagementService,
    private readonly outboundService: OutboundService
  ) {}

  // ==================== 入库管理 ====================

  @Post('inbound')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '入库操作' })
  @ApiResponse({ status: 200, description: '入库成功' })
  async inbound(@Body() inboundData: any) {
    return this.warehouseManagementService.inbound(inboundData)
  }

  // ==================== 出库管理 ====================

  @Post('batch-outbound')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批量出库操作 - 统一接口处理多仓库多类型包裹' })
  @ApiResponse({ status: 200, description: '批量出库成功' })
  async batchOutbound(@Body() batchOutboundData: any) {
    return this.warehouseManagementService.batchOutbound(batchOutboundData)
  }

  // ==================== 移库管理 ====================

  @Post('transfer')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '移库操作' })
  @ApiResponse({ status: 200, description: '移库成功' })
  async transfer(@Body() transferData: any) {
    return this.warehouseManagementService.transfer(transferData)
  }

  // ==================== 盘存管理 ====================

  @Post('inventory-check')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '盘存操作' })
  @ApiResponse({ status: 200, description: '盘存成功' })
  async inventoryCheck(@Body() inventoryData: any) {
    return this.warehouseManagementService.inventoryCheck(inventoryData)
  }

  @Get('search-shipped-clothing')
  @ApiOperation({ summary: '搜索已出库服装（用于盘盈）' })
  @ApiResponse({ status: 200, description: '搜索成功' })
  async searchShippedClothing(@Query() params: any) {
    return this.warehouseManagementService.searchShippedClothing(params)
  }

  @Get('clothing-names-for-search')
  @ApiOperation({ summary: '获取所有仓库包裹的服装名称用于搜索下拉选项' })
  @ApiResponse({ status: 200, description: '获取服装名称列表成功' })
  async getClothingNamesForSearch() {
    return this.warehouseManagementService.getClothingNamesForSearch()
  }



  // ==================== 库存查询 ====================

  @Get('inventory')
  @ApiOperation({ summary: '查看库存' })
  @ApiResponse({ status: 200, description: '获取库存成功' })
  async getInventory(
    @Query('warehouse_id') warehouse_id?: string,
    @Query('sku') sku?: string,
    @Query('product_name') product_name?: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20'
  ) {
    const params = {
      warehouse_id,
      sku,
      product_name,
      page: parseInt(page),
      limit: parseInt(limit)
    }
    return this.warehouseManagementService.getInventory(params)
  }

  @Get('inventory-summary')
  @ApiOperation({ summary: '获取全局库存汇总统计' })
  @ApiResponse({ status: 200, description: '获取全局库存汇总成功' })
  async getGlobalInventorySummary() {
    return this.warehouseManagementService.getGlobalInventorySummary()
  }

  // ==================== 操作日志 ====================

  @Get('operation-logs-detail')
  @ApiOperation({ summary: '获取操作日志明细' })
  @ApiResponse({ status: 200, description: '获取操作日志明细成功' })
  async getOperationLogsDetail(
    @Query('warehouse_id') warehouse_id?: string,
    @Query('operation_type') operation_type?: string,
    @Query('clothing_name') clothing_name?: string,
    @Query('clothing_id') clothing_id?: string,
    @Query('oem_clothing_id') oem_clothing_id?: string,
    @Query('start_date') start_date?: string,
    @Query('end_date') end_date?: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20'
  ) {
    const params = {
      warehouse_id,
      operation_type,
      clothing_name,
      clothing_id,
      oem_clothing_id,
      start_date,
      end_date,
      page: parseInt(page),
      limit: parseInt(limit)
    }
    return this.warehouseManagementService.getOperationLogsDetail(params)
  }

  @Get('operation-logs-summary')
  @ApiOperation({ summary: '获取操作日志汇总' })
  @ApiResponse({ status: 200, description: '获取操作日志汇总成功' })
  async getOperationLogsSummary(
    @Query('warehouse_id') warehouse_id?: string,
    @Query('operation_type') operation_type?: string,
    @Query('clothing_name') clothing_name?: string,
    @Query('start_date') start_date?: string,
    @Query('end_date') end_date?: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20'
  ) {
    const params = {
      warehouse_id,
      operation_type,
      clothing_name,
      start_date,
      end_date,
      page: parseInt(page),
      limit: parseInt(limit)
    }
    return this.warehouseManagementService.getOperationLogsSummary(params)
  }

  // 保持向后兼容的旧接口，默认使用汇总方法
  @Get('operation-logs')
  @ApiOperation({ summary: '获取操作日志（默认汇总）' })
  @ApiResponse({ status: 200, description: '获取操作日志成功' })
  async getOperationLogs(
    @Query('warehouse_id') warehouse_id?: string,
    @Query('operation_type') operation_type?: string,
    @Query('clothing_name') clothing_name?: string,
    @Query('start_date') start_date?: string,
    @Query('end_date') end_date?: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20'
  ) {
    const params = {
      warehouse_id,
      operation_type,
      clothing_name,
      start_date,
      end_date,
      page: parseInt(page),
      limit: parseInt(limit)
    }
    return this.warehouseManagementService.getOperationLogsSummary(params)
  }

  @Get('inbound-statistics')
  @ApiOperation({ summary: '获取已入库统计数据' })
  @ApiResponse({ status: 200, description: '获取统计数据成功' })
  async getInboundStatistics(
    @Query('transportation_id') transportation_id: string
  ) {
    return this.warehouseManagementService.getInboundStatistics(transportation_id)
  }

  @Get('warehouses-with-stats')
  @ApiOperation({ summary: '获取包含统计数据的仓库列表' })
  @ApiResponse({ status: 200, description: '获取仓库列表成功' })
  async getWarehousesWithStats(
    @Query('status') status: string = 'active'
  ) {
    return this.warehouseManagementService.getWarehousesWithStats(status)
  }

  @Post('reverse-operation-log')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '冲正操作日志' })
  @ApiResponse({ status: 200, description: '冲正成功' })
  async reverseOperationLog(@Body() reverseData: { log_id: string; operator_name: string }) {
    return this.warehouseManagementService.reverseOperationLog(reverseData)
  }

  @Post('reverse-transfer-operation')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '移库操作专用冲正' })
  @ApiResponse({ status: 200, description: '移库冲正成功' })
  async reverseTransferOperation(@Body() reverseData: { log_id: string; operator_name: string }) {
    // 直接调用通用冲正方法，它会自动识别移库操作并调用专门的移库冲正逻辑
    return this.warehouseManagementService.reverseOperationLog(reverseData)
  }

  // ==================== 日历相关 ====================

  @Get('daily-outbound-summary')
  @ApiOperation({ summary: '获取每日出库汇总数据' })
  @ApiResponse({ status: 200, description: '获取每日出库汇总成功' })
  async getDailyOutboundSummary(
    @Query('year') year: string,
    @Query('month') month: string,
    @Query('warehouse_id') warehouse_id?: string,
    @Query('clothing_id') clothing_id?: string,
    @Query('oem_clothing_id') oem_clothing_id?: string
  ) {
    const params = {
      year: parseInt(year),
      month: parseInt(month),
      warehouse_id,
      clothing_id,
      oem_clothing_id
    }
    return this.outboundService.getDailyOutboundSummary(params)
  }

  @Get('daily-inbound-summary')
  @ApiOperation({ summary: '获取每日入库汇总数据' })
  @ApiResponse({ status: 200, description: '获取每日入库汇总成功' })
  async getDailyInboundSummary(
    @Query('year') year: string,
    @Query('month') month: string,
    @Query('warehouse_id') warehouse_id?: string,
    @Query('clothing_id') clothing_id?: string,
    @Query('oem_clothing_id') oem_clothing_id?: string
  ) {
    const params = {
      year: parseInt(year),
      month: parseInt(month),
      warehouse_id,
      clothing_id,
      oem_clothing_id
    }
    return this.inboundService.getDailyInboundSummary(params)
  }
}
