/* components/calendar-table/index.wxss */

.calendar-container {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  overflow: hidden;
  position: relative;
}

/* 日历头部 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 20rpx;
  background: linear-gradient(135deg, #2c9678 0%, #3ba688 100%);
  color: white;
}

.header-controls {
  display: flex;
  align-items: center;
  flex: 1;
}

.nav-button {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 48rpx;
}

.nav-icon {
  font-size: 36rpx;
  font-weight: bold;
}

.date-selector {
  flex: 1.5;
  display: flex;
}

.year-month-display {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-evenly;
}

.year-text,
.month-text {
  font-size: 16rpx;
  font-weight: 500;
  margin-right: 2rpx;
}

.dropdown-icon {
  font-size: 24rpx;
  margin-left: 10rpx;
  opacity: 0.8;
}

.today-button {
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  font-size: 24rpx;
}

/* 星期标题 */
.week-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.week-day {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
}

/* 日历主体 */
.calendar-body {
  background: #fff;
  transition: opacity 0.3s ease;
  /* 优化渲染性能 */
  contain: layout style paint;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 0;
}

.calendar-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 80rpx; /* 固定高度，使日历更紧凑 */
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.15s ease;
  will-change: background-color;
  transform: translateZ(0); /* 启用硬件加速 */
  /* 防止点击时的抖动 */
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* 移除每行最后一列的右边框 */
.calendar-cell:nth-child(7n) {
  border-right: none;
}

.calendar-cell.current-month {
  background: #fff;
  color: #333;
}

.calendar-cell.other-month {
  background: #fafafa;
  color: #ccc;
}

.calendar-cell.today {
  background: #e8f5f1;
}

.calendar-cell.selected {
  background: #2c9678 !important;
  color: white !important;
  transform: translateZ(0);
}

.calendar-cell.selected .outbound-count {
  color: #962c46;
}

/* 区间选择中间日期样式 */
.calendar-cell.in-range {
  background: rgba(44, 150, 120, 0.2) !important;
  color: #2c9678 !important;
}

.calendar-cell.range-start {
  background: #2c9678 !important;
  color: white !important;
}

.calendar-cell.range-end {
  background: #2c9678 !important;
  color: white !important;
}

.calendar-cell.range-start.range-end {
  border-radius: 50%;
}

.date-number {
  font-size: 20rpx;
  font-weight: 500;
  margin-bottom: 2rpx;
}

.outbound-count {
  font-size: 28rpx;
  font-weight: 500;
  color: #962c46;
  padding: 1rpx 4rpx;
  border-radius: 6rpx;
  min-width: 16rpx;
  text-align: center;
  line-height: 1.1;
}

/* 入库状态指示器 - 右上角小蓝点 */
.inbound-indicator {
  position: absolute;
  top: 4rpx;
  right: 4rpx;
  width: 12rpx;
  height: 12rpx;
  background: #1890ff; /* 蓝色指示器 */
  border-radius: 50%;
  border: 1rpx solid #ffffff;
  box-shadow: 0 1rpx 3rpx rgba(24, 144, 255, 0.3);
  z-index: 2;
}

/* 底部信息和查询按钮 */
.calendar-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.selected-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.date-info {
  font-size: 20rpx;
  color: #666;
}

.outbound-total {
  font-size: 24rpx;
  color: #2c9678;
  font-weight: 500;
}

.query-button {
  padding: 8rpx 20rpx;
  background: #2c9678;
  color: white;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
}

/* 选择器弹窗 */
.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #fff;
}

.picker-cancel,
.picker-confirm {
  font-size: 16px;
  color: #2c9678;
}

.picker-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 确保弹窗有足够高的层级 */
:host {
  z-index: 9999 !important;
}

/* van-picker 组件样式 */
van-picker {
  background: #fff;
  width: 100%;
}

/* 确保 van-popup 内容可见 */
van-popup {
  z-index: 9999 !important;
}

van-popup .van-popup__content {
  background: #fff;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .calendar-container {
    margin: 10px;
  }

  .calendar-header {
    padding: 12px 16px;
  }

  .year-text,
  .month-text {
    font-size: 16px;
  }

  .date-number {
    font-size: 14px;
  }

  .outbound-count {
    font-size: 9px;
  }
}
